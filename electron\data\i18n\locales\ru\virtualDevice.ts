/**
 * Virtual device Russian language pack
 */
export default {
  menus: {
    analog: {
      name: "Аналоговый",
      desc: "Аналоговые параметры"
    },
    digitalInput: {
      name: "Цифровой Вход",
      desc: "Параметры цифрового входа"
    },
    digitalOutput: {
      name: "Цифровой Выход",
      desc: "Параметры цифрового выхода"
    },
    fault: {
      name: "Неполадка",
      desc: "Параметры неполадки"
    },
    led: {
      name: "Параметры LED",
      desc: "Параметры LED"
    },
    waveReplay: {
      name: "Воспроизведение Неполадки",
      desc: "Воспроизведение формы волны неполадки"
    }
  },
  items: {
    analog: {
      name: "Аналоговый Вход 1",
      desc: "Аналоговый Вход 1"
    },
    digitalInput: {
      name: "Ц<PERSON><PERSON>ров<PERSON>й Вход 1",
      desc: "Цифровой Вход 1"
    },
    digitalOutput: {
      name: "Цифровой Выход 1",
      desc: "Цифровой Выход 1"
    },
    fault: {
      name: "Сигнал Неполадки 1",
      desc: "Сигнал Неполадки 1"
    },
    led: {
      name: "Яркость LED",
      desc: "Яркость LED"
    },
    waveReplay: {
      name: "Файл Формы Волны",
      desc: "Файл Формы Волны"
    }
  },
  // Связанное с импортом/экспортом
  importExport: {
    export: {
      success: "Экспорт успешен",
      failed: "Экспорт не удался",
      noData: "Нет данных для экспорта",
      invalidPath: "Недопустимый путь к файлу"
    },
    import: {
      success: "Импорт успешен",
      failed: "Импорт не удался",
      parsing: "Анализ файла Excel...",
      validating: "Проверка данных...",
      updating: "Обновление параметров..."
    },
    template: {
      export: "Экспорт Шаблона",
      name: "Шаблон Импорта"
    }
  },
  // Заголовки колонок Excel
  columns: {
    index: "Индекс",
    name: "Имя", 
    description: "Описание",
    dataValue: "Значение Данных",
    ang: "Угол"
  },
  // Сообщения об ошибках проверки
  validation: {
    required: {
      cmdType: "Тип команды не может быть пустым",
      filePath: "Путь к файлу не может быть пустым",
      name: "Строка {row}: Поле имени не может быть пустым"
    },
    format: {
      fileExtension: "Поддерживаются только файлы Excel в формате .xlsx",
      dataValue: "Строка {row}: Значение данных должно быть действительным числом",
      ang: "Строка {row}: Угол должен быть действительным числом"
    },
    range: {
      angValue: "Строка {row}: Значение угла должно быть между -360 и 360 градусами"
    },
    file: {
      notExists: "Файл не существует: {filePath}",
      noValidData: "Нет действительных данных в файле Excel",
      noValidRows: "Нет действительных данных для импорта",
      parseError: "Не удалось проанализировать файл Excel: {error}",
      exportError: "Не удалось экспортировать файл Excel: {error}"
    },
    data: {
      validationFailed: "Проверка данных не удалась:\n{errors}",
      updateFailed: "Данные импорта успешно проанализированы, но обновление не удалось"
    }
  },
  // Сообщения об операциях
  messages: {
    exportStart: "Начало экспорта параметров виртуального устройства",
    exportSuccess: "Параметры виртуального устройства успешно экспортированы: {filePath}, количество данных: {count}",
    importStart: "Начало импорта параметров виртуального устройства",
    importSuccess: "Параметры виртуального устройства успешно импортированы и обновлены: {filePath}, количество обновлений: {count}",
    parseSuccess: "Файл Excel успешно проанализирован, количество данных: {count}",
    validDataCount: "Количество действительных данных: {count}",
    templateExportSuccess: "Шаблон импорта успешно экспортирован: {filePath}"
  }
};