/**
 * Virtual device English language pack
 */
export default {
  menus: {
    analog: {
      name: "Analog",
      desc: "Analog parameters"
    },
    digitalInput: {
      name: "Digital Input",
      desc: "Digital input parameters"
    },
    digitalOutput: {
      name: "Digital Output",
      desc: "Digital output parameters"
    },
    fault: {
      name: "Fault",
      desc: "Fault parameters"
    },
    led: {
      name: "LED Parameters",
      desc: "LED parameters"
    },
    waveReplay: {
      name: "Fault Replay",
      desc: "Fault waveform replay"
    }
  },
  items: {
    analog: {
      name: "Analog Input 1",
      desc: "Analog Input 1"
    },
    digitalInput: {
      name: "Digital Input 1",
      desc: "Digital Input 1"
    },
    digitalOutput: {
      name: "Digital Output 1",
      desc: "Digital Output 1"
    },
    fault: {
      name: "Fault Signal 1",
      desc: "Fault Signal 1"
    },
    led: {
      name: "LED Brightness",
      desc: "LED Brightness"
    },
    waveReplay: {
      name: "Waveform File",
      desc: "Waveform File"
    }
  },
  // Import/Export related
  importExport: {
    export: {
      success: "Export successful",
      failed: "Export failed",
      noData: "No data to export",
      invalidPath: "Invalid file path"
    },
    import: {
      success: "Import successful",
      failed: "Import failed",
      parsing: "Parsing Excel file...",
      validating: "Validating data...",
      updating: "Updating parameters..."
    },
    template: {
      export: "Export Template",
      name: "Import Template"
    }
  },
  // Excel column headers
  columns: {
    index: "Index",
    name: "Name", 
    description: "Description",
    dataValue: "Data Value",
    ang: "Angle"
  },
  // Validation error messages
  validation: {
    required: {
      cmdType: "Command type cannot be empty",
      filePath: "File path cannot be empty",
      name: "Row {row}: Name field cannot be empty"
    },
    format: {
      fileExtension: "Only .xlsx format Excel files are supported",
      dataValue: "Row {row}: Data value must be a valid number",
      ang: "Row {row}: Angle must be a valid number"
    },
    range: {
      angValue: "Row {row}: Angle value should be between -360 and 360 degrees"
    },
    file: {
      notExists: "File does not exist: {filePath}",
      noValidData: "No valid data in Excel file",
      noValidRows: "No valid data to import",
      parseError: "Failed to parse Excel file: {error}",
      exportError: "Failed to export Excel file: {error}"
    },
    data: {
      validationFailed: "Data validation failed:\n{errors}",
      updateFailed: "Import data parsed successfully, but update failed"
    }
  },
  // Operation messages
  messages: {
    exportStart: "Starting to export virtual device parameters",
    exportSuccess: "Successfully exported virtual device parameters: {filePath}, data count: {count}",
    importStart: "Starting to import virtual device parameters",
    importSuccess: "Successfully imported and updated virtual device parameters: {filePath}, update count: {count}",
    parseSuccess: "Excel file parsed successfully, data count: {count}",
    validDataCount: "Valid data count: {count}",
    templateExportSuccess: "Successfully exported import template: {filePath}"
  }
};