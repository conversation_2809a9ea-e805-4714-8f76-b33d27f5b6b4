"use strict";
import { deviceConnectService } from "../../service/debug/deviceconnect";
import { deviceFileService } from "../../service/debug/devicefile";

import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";
import {
  ERROR_CODES,
  ERROR_MESSAGES,
  handleErrorResponse,
  handleConnectionError,
  handleCustomResponse,
} from "../../data/debug/errorCodes";
import { ApiResponse } from "../../data/debug/apiResponse";
import { IECReq } from "../../interface/debug/request";

/**
 * Z装置文件服务
 * <AUTHOR>
 * @class
 */
class DeviceFileController {
  constructor() {
    logger.info(
      `[DeviceFileController] ${t("logs.deviceFileController.initialized")}`
    );
  }

  /**
   * 获取装置目录
   * getDeviceFile
   */
  async getDeviceFile(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[DeviceFileController] ${t("logs.deviceFileController.getDeviceFileStart")}:`,
      req
    );

    try {
      // 检查装置是否连接，未连接返回空
      logger.debug(
        `[DeviceFileController] ${t("logs.deviceFileController.getDeviceFileCheckConnection")}`
      );
      const connected = await deviceConnectService.checkConnection(req);

      if (!connected) {
        logger.warn(
          `[DeviceFileController] ${t("logs.deviceFileController.getDeviceFileNotConnected")}`
        );
        return handleConnectionError();
      }

      logger.debug(
        `[DeviceFileController] ${t("logs.deviceFileController.getDeviceFileConnected")}`
      );
      const result = await deviceFileService.getDeviceFile(req);

      logger.info(
        `[DeviceFileController] ${t("logs.deviceFileController.getDeviceFileSuccess")}`
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        `[DeviceFileController] ${t("logs.deviceFileController.getDeviceFileException")}:`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 获取装置目录（分页）
   * getDeviceFilePage
   */
  async getDeviceFilePage(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(`[DeviceFileController] 获取装置文件分页数据开始:`, req);

    try {
      // 检查装置是否连接，未连接返回空
      logger.debug(`[DeviceFileController] 检查装置连接状态`);
      const connected = await deviceConnectService.checkConnection(req);

      if (!connected) {
        logger.warn(`[DeviceFileController] 装置未连接，无法获取文件列表`);
        return handleConnectionError();
      }

      logger.debug(`[DeviceFileController] 装置已连接，开始获取分页数据`);
      const result = await deviceFileService.getDeviceFilePage(req);

      logger.info(`[DeviceFileController] 获取装置文件分页数据成功`);
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(`[DeviceFileController] 获取装置文件分页数据异常:`, error);
      return handleErrorResponse(error);
    }
  }

  /**
   * 上传装置文件
   * uploadDeviceFile
   */
  async uploadDeviceFile(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[DeviceFileController] ${t("logs.deviceFileController.uploadDeviceFileStart")}:`,
      req
    );

    try {
      // 检查装置是否连接，未连接返回空
      logger.debug(
        `[DeviceFileController] ${t("logs.deviceFileController.uploadDeviceFileCheckConnection")}`
      );
      const connected = await deviceConnectService.checkConnection(req);

      if (!connected) {
        logger.warn(
          `[DeviceFileController] ${t("logs.deviceFileController.uploadDeviceFileNotConnected")}`
        );
        return handleConnectionError();
      }

      logger.debug(
        `[DeviceFileController] ${t("logs.deviceFileController.uploadDeviceFileConnected")}`
      );
      const result = await deviceFileService.uploadDeviceFile(req);

      logger.info(
        `[DeviceFileController] ${t("logs.deviceFileController.uploadDeviceFileSuccess")}:`,
        result
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        `[DeviceFileController] ${t("logs.deviceFileController.uploadDeviceFileException")}:`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 取消上传装置文件
   * cancelUploadDeviceFIle
   */
  async cancelUploadDeviceFIle(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[DeviceFileController] cancelUploadDeviceFIle - 开始取消文件上传，请求参数:`,
      req
    );

    try {
      // 检查装置是否连接，未连接返回空
      logger.debug(
        `[DeviceFileController] cancelUploadDeviceFIle - 检查设备连接状态`
      );
      const connected = await deviceConnectService.checkConnection(req);

      if (!connected) {
        logger.warn(
          `[DeviceFileController] cancelUploadDeviceFIle - 设备未连接，无法取消上传`
        );
        return handleConnectionError();
      }

      logger.debug(
        `[DeviceFileController] cancelUploadDeviceFIle - 设备已连接，调用服务层取消上传`
      );
      const result = await deviceFileService.cancelUploadDeviceFIle(req);

      logger.info(
        `[DeviceFileController] cancelUploadDeviceFIle - 取消上传成功，结果:`,
        result
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        `[DeviceFileController] cancelUploadDeviceFIle - 取消上传异常:`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 下载装置文件
   * downloadDeviceFile
   */
  async downloadDeviceFile(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[DeviceFileController] downloadDeviceFile - 开始下载设备文件，请求参数:`,
      req
    );

    try {
      // 检查装置是否连接，未连接返回空
      logger.debug(
        `[DeviceFileController] downloadDeviceFile - 检查设备连接状态`
      );
      const connected = await deviceConnectService.checkConnection(req);

      if (!connected) {
        logger.warn(
          `[DeviceFileController] downloadDeviceFile - 设备未连接，无法下载文件`
        );
        return handleConnectionError();
      }

      logger.debug(
        `[DeviceFileController] downloadDeviceFile - 设备已连接，调用服务层下载文件`
      );
      const result = await deviceFileService.downloadDeviceFile(req);

      logger.info(
        `[DeviceFileController] downloadDeviceFile - 文件下载成功，结果:`,
        result
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        `[DeviceFileController] downloadDeviceFile - 文件下载异常:`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 取消下载装置文件
   * cancelDownloadDeviceFile
   */
  async cancelDownloadDeviceFile(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[DeviceFileController] cancelDownloadDeviceFile - 开始取消文件下载，请求参数:`,
      req
    );

    try {
      // 检查装置是否连接，未连接返回空
      logger.debug(
        `[DeviceFileController] cancelDownloadDeviceFile - 检查设备连接状态`
      );
      const connected = await deviceConnectService.checkConnection(req);

      if (!connected) {
        logger.warn(
          `[DeviceFileController] cancelDownloadDeviceFile - 设备未连接，无法取消下载`
        );
        return handleConnectionError();
      }

      logger.debug(
        `[DeviceFileController] cancelDownloadDeviceFile - 设备已连接，调用服务层取消下载`
      );
      const result = await deviceFileService.cancelDownloadDeviceFile(req);

      logger.info(
        `[DeviceFileController] cancelDownloadDeviceFile - 取消下载成功，结果:`,
        result
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        `[DeviceFileController] cancelDownloadDeviceFile - 取消下载异常:`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  async importDownloadDeviceFile(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[DeviceFileController] importDownloadDeviceFile - 开始导入下载的设备文件，请求参数:`,
      req
    );

    if (!(await deviceConnectService.checkConnection(req))) {
      logger.warn(
        `[DeviceFileController] importDownloadDeviceFile - 设备未连接，无法导入文件`
      );
      return handleConnectionError();
    }

    try {
      logger.debug(
        `[DeviceFileController] importDownloadDeviceFile - 设备已连接，调用服务层导入文件`
      );
      const flag = await deviceFileService.importDownloadDeviceFile(req);

      logger.info(
        `[DeviceFileController] importDownloadDeviceFile - 文件导入成功，结果:`,
        flag
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        flag
      );
    } catch (error) {
      logger.error(
        `[DeviceFileController] importDownloadDeviceFile - 文件导入异常:`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  async exportDownloadDeviceFile(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[DeviceFileController] exportDownloadDeviceFile - 开始导出下载的设备文件，请求参数:`,
      req
    );

    if (!(await deviceConnectService.checkConnection(req))) {
      logger.warn(
        `[DeviceFileController] exportDownloadDeviceFile - 设备未连接，无法导出文件`
      );
      return handleConnectionError();
    }

    const { path } = req.data;
    logger.debug(
      `[DeviceFileController] exportDownloadDeviceFile - 导出路径: ${path}`
    );

    // 检查导出路径是否为空
    if (!path) {
      logger.error(
        `[DeviceFileController] ${t("logs.deviceFileController.exportDownloadDeviceFileEmptyPath")}`
      );
      return handleCustomResponse(
        ERROR_CODES.INVALID_PARAM,
        t("errors.exportPathEmpty")
      );
    }

    try {
      logger.debug(
        `[DeviceFileController] exportDownloadDeviceFile - 调用服务层导出文件`
      );
      const flag = await deviceFileService.exportDownloadDeviceFile(req);

      logger.info(
        `[DeviceFileController] exportDownloadDeviceFile - 文件导出成功，结果:`,
        flag
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        flag
      );
    } catch (error) {
      logger.error(
        `[DeviceFileController] exportDownloadDeviceFile - 文件导出异常:`,
        error
      );
      return handleErrorResponse(error);
    }
  }
}

DeviceFileController.toString = () => "[class DeviceFileController]";
export default DeviceFileController;
