<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :cell-style="cellStyle"
      :columns="columns"
      highlight-current-row
      table-key="virtualLedParam"
      :request-api="getParamList"
      :init-param="initParam"
      :request-auto="false"
      row-key="name"
      :data-callback="dataCallback"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <div class="table-header-wrapper">
          <div class="header-left">
            <el-checkbox v-model="refreshCheck" :label="t('device.virtualParam.autoRefresh')" size="large" />
          </div>
          <div class="header-right">
            <div class="button-group">
              <el-button type="primary" plain :icon="Refresh" @click="handleButtonClick('refresh')">{{ t("device.virtualParam.refresh") }}</el-button>
              <el-button type="success" :icon="Download" @click="handleButtonClick('export')">{{ t("device.virtualParam.export") }}</el-button>
            </div>
          </div>
        </div>
      </template>
    </ProTable>
  </div>
  <ProgressDialog ref="progressDialog"></ProgressDialog>
</template>

<script setup lang="tsx" name="VirtualLedParam">
import { ref, reactive, onUnmounted, onBeforeUnmount, onMounted, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import { virtualDeviceApi } from "@/api/modules/biz/debug/virtualDevice";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { Refresh, Download } from "@element-plus/icons-vue";
import { h } from "vue";
import { osControlApi } from "@/api/modules/biz/os";
import { useDebugStore } from "@/stores/modules/debug";
import { useConfigStore } from "@/stores/modules";
import ProgressDialog from "../../dialog/ProgressDialog.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const { addConsole } = useDebugStore();
const { paramInfo } = useConfigStore();

// 定义 props
const props = defineProps<{ deviceId: string }>();
const progressDialog = ref();
const refreshCheck = ref(false);

// ProTable 实例
const proTable = ref<ProTableInstance>();

// 初始化请求参数
const initParam = reactive({
  cmdType: "read_led_para",
  deviceId: props.deviceId // 确保初始化参数中包含deviceId
});

// 监听deviceId变化
watch(
  () => props.deviceId,
  newVal => {
    if (newVal && proTable.value) {
      // 当deviceId变化时重新加载数据
      initParam.deviceId = newVal;
      proTable.value.getTableList();
    }
  }
);

const showLoading = () => {
  if (progressDialog.value) {
    progressDialog.value.show();
  }
};
const hideLoading = () => {
  if (progressDialog.value) {
    progressDialog.value.hide();
  }
};

// 归一化颜色键的通用函数
const normalizeColorKey = (s: string): "R" | "G" | "B" | "Y" | "O" | "P" | "W" | "C" | "M" | "OFF" | "ON" | "UNKNOWN" => {
  const sTrim = s.trim();
  const sUpper = sTrim.toUpperCase();

  // 直接匹配标准键
  if (["R", "G", "B", "Y", "O", "P", "W", "C", "M", "OFF", "ON"].includes(sUpper)) return sUpper as any;

  // 英文单词
  if (sUpper.includes("RED")) return "R";
  if (sUpper.includes("GREEN")) return "G";
  if (sUpper.includes("BLUE")) return "B";
  if (sUpper.includes("YELLOW")) return "Y";
  if (sUpper.includes("ORANGE")) return "O";
  if (sUpper.includes("PURPLE") || sUpper.includes("VIOLET")) return "P";
  if (sUpper.includes("WHITE")) return "W";
  if (sUpper.includes("CYAN")) return "C";
  if (sUpper.includes("MAGENTA")) return "M";
  if (sUpper.includes("OFF") || sUpper === "0") return "OFF";
  if (sUpper.includes("ON") || sUpper === "1") return "ON";

  // 中文关键字
  if (/红/.test(sTrim)) return "R";
  if (/绿/.test(sTrim)) return "G";
  if (/蓝/.test(sTrim)) return "B";
  if (/黄/.test(sTrim)) return "Y";
  if (/橙/.test(sTrim)) return "O";
  if (/紫/.test(sTrim)) return "P";
  if (/白/.test(sTrim)) return "W";
  if (/青/.test(sTrim)) return "C";
  if (/品红|洋红/.test(sTrim)) return "M";
  if (/关|关闭|灭/.test(sTrim)) return "OFF";
  if (/开|开启|亮/.test(sTrim)) return "ON";

  return "UNKNOWN";
};

// 获取LED CSS类名的通用函数
const getLedClass = (colorKey: string) => {
  const classMap: Record<string, string> = {
    R: "led-red",
    G: "led-green",
    B: "led-blue",
    Y: "led-yellow",
    O: "led-orange",
    P: "led-purple",
    W: "led-white",
    C: "led-cyan",
    M: "led-magenta",
    OFF: "led-off",
    ON: "led-on"
  };
  return classMap[colorKey] || "led-unknown";
};

// 获取国际化的颜色名称
const getColorName = (key: string) => {
  try {
    return t(`device.ledColors.${key}`);
  } catch {
    return key;
  }
};

// LED颜色图标渲染函数（只显示颜色圆点）
const renderLedColorDot = (value: any) => {
  // 处理 null、undefined 和空字符串的情况，但保留数字 0
  if (value === null || value === undefined || (typeof value === "string" && value.trim() === "")) {
    return h("div", {
      class: "led-color-dot led-unknown"
    });
  }

  // 确保value是字符串类型，包括数字 0
  const valueStr = String(value).trim();
  const colorKey = normalizeColorKey(valueStr);

  return h("div", {
    class: `led-color-dot ${getLedClass(colorKey)}`
  });
};

// LED颜色文本渲染函数（只显示颜色描述）
const renderLedColorText = (value: any) => {
  // 处理 null、undefined 和空字符串的情况，但保留数字 0
  if (value === null || value === undefined || (typeof value === "string" && value.trim() === "")) {
    return h("span", { style: { color: "#999" } }, "-");
  }

  // 确保value是字符串类型，包括数字 0
  const valueStr = String(value).trim();

  const colorKey = normalizeColorKey(valueStr);
  const colorName = colorKey !== "UNKNOWN" ? getColorName(colorKey) : getColorName("UNKNOWN");

  // 显示文本：若 value 自带颜色名，则不再附加括号
  const showText = (() => {
    if (/红|绿|蓝|黄|橙|紫|白|青|品红|关|开/.test(valueStr)) return valueStr;
    if (/(red|green|blue|yellow|orange|purple|white|cyan|magenta|off|on)/i.test(valueStr)) return valueStr;
    return `${valueStr} (${colorName})`;
  })();

  return h(
    "span",
    {
      class: "led-color-text"
    },
    showText
  );
};

// 处理返回的表格数据
const dataCallback = async (data: any): Promise<{ list: any[]; total: number }> => {
  // hideLoading();
  try {
    if (!data || !Array.isArray(data.list)) {
      console.warn("Invalid data received:", data);
      return { list: [], total: 0 };
    }

    // 直接返回数据
    return {
      list: data.list,
      total: data.total
    };
  } catch (error) {
    console.error("Error in dataCallback:", error);
    throw error;
  }
};

// 获取参数列表
const getParamList = async (params: any) => {
  console.log("=== getParamList START ===");
  console.log("getParamList called with params:", JSON.stringify(params, null, 2));
  console.log("props.deviceId:", props.deviceId);

  try {
    // 创建新的参数对象
    let newParams = JSON.parse(JSON.stringify(params));
    newParams.cmdType = "read_led_para";

    console.log("📱 Using deviceId:", props.deviceId);
    console.log("📡 Calling API...");

    // 使用与VirtualDigitalInput相同的API调用方式
    const result = await virtualDeviceApi.getVirtualParamsByDevice(props.deviceId, "read_led_para", newParams);

    console.log("📡 API response:", JSON.stringify(result, null, 2));
    return result || { list: [], total: 0 };
  } catch (error) {
    console.error("❌ getParamList error:", error);
    ElMessage.error("获取数据失败");
    return { list: [], total: 0 };
  }
};

// 组件挂载后自动加载数据
onMounted(() => {
  console.log("🚀 Component mounted, loading data...");
  setTimeout(() => {
    if (proTable.value) {
      proTable.value.getTableList();
    }
  }, 100);
});

// 表格配置项
const columns = reactive<ColumnProps[]>([
  { type: "selection", fixed: "left", width: 50 },
  {
    prop: "index",
    label: t("device.virtualParam.sequenceNumber")
  },
  {
    prop: "name",
    label: t("device.virtualParam.name"),
    search: {
      el: "input",
      tooltip: t("device.virtualParam.inputName"),
      props: {
        onKeyup: (e: KeyboardEvent) => {
          if (e.key === "Enter") {
            proTable.value?.search();
          }
        }
      }
    }
  },
  {
    prop: "description",
    label: t("device.virtualParam.description"),
    search: {
      el: "input",
      tooltip: t("device.virtualParam.inputDescription"),
      props: {
        onKeyup: (e: KeyboardEvent) => {
          if (e.key === "Enter") {
            proTable.value?.search();
          }
        }
      }
    }
  },
  {
    prop: "colorIcon",
    label: t("device.virtualParam.colorIcon"),
    width: 80,
    render: (scope: any) => {
      return renderLedColorDot(scope.row.value);
    }
  },
  {
    prop: "value",
    label: t("device.virtualParam.colorDescription"),
    render: (scope: any) => {
      return renderLedColorText(scope.row.value);
    }
  }
]);

// 按钮处理函数
const handleButtonClick = async (action: string) => {
  switch (action) {
    case "refresh":
      proTable.value?.getTableList();
      break;
    case "export":
      handleExport();
      break;
    default:
      console.log(action);
  }
};

// 导出处理
const handleExport = async () => {
  const defaultPath = "虚拟装置_LED参数.xlsx";
  const selectPath = await osControlApi.openSaveFileDialogByParams({
    title: t("device.virtualParam.exportTitle"),
    defaultPath,
    filterList: [{ name: "xlsx", extensions: ["xlsx"] }]
  });
  if (!selectPath) {
    return;
  }
  console.log("selectPath:", selectPath);
  const path = String(selectPath);
  showLoading();
  try {
    const response = await virtualDeviceApi.exportVirtualParams(props.deviceId, "read_led_para", path);
    if (response && response.code === 0) {
      ElMessageBox.alert(t("device.virtualParam.exportSuccess") + path, t("device.virtualParam.success"), {
        confirmButtonText: t("device.virtualParam.confirm"),
        type: "success"
      });
      addConsole(t("device.virtualParam.exportSuccess") + path);
    } else {
      ElMessageBox.alert(response?.msg || t("device.virtualParam.exportFailed"), t("device.virtualParam.error"), {
        confirmButtonText: t("device.virtualParam.confirm"), 
        type: "error"
      });
      addConsole(`${t("device.virtualParam.exportFailed")}: ${response?.msg || "Unknown error"}`);
    }
  } catch (error) {
    console.error("Export error:", error);
    ElMessageBox.alert(t("device.virtualParam.exportFailed"), t("device.virtualParam.error"), {
      confirmButtonText: t("device.virtualParam.confirm"),
      type: "error"
    });
    addConsole(`${t("device.virtualParam.exportFailed")}: ${error}`);
  } finally {
    hideLoading();
  }
};


// 单元格样式
const cellStyle = ({ row }: { row: any; column: any; rowIndex: number; columnIndex: number }) => {
  console.log(row);
  return {};
};

// 监听自动刷新复选框
watch(refreshCheck, newValue => {
  if (newValue) {
    startRefreshTimer();
  } else {
    stopRefreshTimer();
  }
});

// 定时器相关逻辑
let refreshTimer: NodeJS.Timeout | null = null;
const startRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
  refreshTimer = setInterval(() => {
    proTable.value?.getTableList();
  }, 1000); // 使用配置的刷新间隔，默认5秒
};

const stopRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
};

onUnmounted(() => {
  stopRefreshTimer();
});

onBeforeUnmount(() => {
  stopRefreshTimer();
});
</script>

<style lang="css" scoped>
.table-box {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
}
.header {
  margin-bottom: 5px;
}

/* LED颜色圆点样式 */

:deep(.led-color-dot) {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  transition: all 0.3s ease;
  cursor: pointer;
  flex-shrink: 0;
}

:deep(.led-color-dot:hover) {
  transform: scale(1.15);
  filter: brightness(1.1);
}

/* 特殊的LED发光效果 */
:deep(.led-color-dot.led-red) {
  background: radial-gradient(circle at 30% 30%, #ff6666, #ff3333);
  box-shadow:
    0 0 10px #ff333340,
    inset 0 0 4px rgba(255, 255, 255, 0.3);
}

:deep(.led-color-dot.led-green) {
  background: radial-gradient(circle at 30% 30%, #66ff66, #33cc33);
  box-shadow:
    0 0 10px #33cc3340,
    inset 0 0 4px rgba(255, 255, 255, 0.3);
}

:deep(.led-color-dot.led-blue) {
  background: radial-gradient(circle at 30% 30%, #6699ff, #3366ff);
  box-shadow:
    0 0 10px #3366ff40,
    inset 0 0 4px rgba(255, 255, 255, 0.3);
}

:deep(.led-color-dot.led-yellow) {
  background: radial-gradient(circle at 30% 30%, #ffdd44, #ffcc00);
  box-shadow:
    0 0 10px #ffcc0040,
    inset 0 0 4px rgba(255, 255, 255, 0.3);
}

:deep(.led-color-dot.led-orange) {
  background: radial-gradient(circle at 30% 30%, #ff9944, #ff6600);
  box-shadow:
    0 0 10px #ff660040,
    inset 0 0 4px rgba(255, 255, 255, 0.3);
}

:deep(.led-color-dot.led-purple) {
  background: radial-gradient(circle at 30% 30%, #ff66ff, #cc33cc);
  box-shadow:
    0 0 10px #cc33cc40,
    inset 0 0 4px rgba(255, 255, 255, 0.3);
}

:deep(.led-color-dot.led-white) {
  background: radial-gradient(circle at 30% 30%, #ffffff, #f0f0f0);
  box-shadow:
    0 0 12px rgba(200, 200, 200, 0.9),
    0 0 6px rgba(150, 150, 150, 0.6),
    inset 0 0 4px rgba(255, 255, 255, 0.4),
    inset 0 0 8px rgba(220, 220, 220, 0.3);
  border: 2px solid #bbb;
  position: relative;
}

:deep(.led-color-dot.led-white::before) {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.8), transparent 60%);
  pointer-events: none;
}

:deep(.led-color-dot.led-cyan) {
  background: radial-gradient(circle at 30% 30%, #44ffff, #00cccc);
  box-shadow:
    0 0 10px #00cccc40,
    inset 0 0 4px rgba(255, 255, 255, 0.3);
}

:deep(.led-color-dot.led-magenta) {
  background: radial-gradient(circle at 30% 30%, #ff44ff, #cc00cc);
  box-shadow:
    0 0 10px #cc00cc40,
    inset 0 0 4px rgba(255, 255, 255, 0.3);
}

:deep(.led-color-dot.led-on) {
  background: radial-gradient(circle at 30% 30%, #44ff44, #00cc00);
  box-shadow:
    0 0 12px #00cc0060,
    inset 0 0 4px rgba(255, 255, 255, 0.4);
  border: 1px solid #00aa00;
}

:deep(.led-color-dot.led-off) {
  background: radial-gradient(circle at 30% 30%, #666, #333);
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.5);
  border: 1px solid #222;
}

:deep(.led-color-dot.led-unknown) {
  background: radial-gradient(circle at 30% 30%, #ccc, #999);
  box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.3);
  border: 1px solid #888;
}

:deep(.led-color-text) {
  font-size: 14px;
  color: var(--el-text-color-primary);
  font-weight: 500;
  white-space: nowrap;
}

/* 表格头部按钮布局样式 */
.table-header-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 8px 0;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: 10px;
}

.button-group {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
