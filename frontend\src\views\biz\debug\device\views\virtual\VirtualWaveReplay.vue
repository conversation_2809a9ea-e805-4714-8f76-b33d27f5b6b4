<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getWaveReplayList"
      :init-param="initParam"
      :pagination="true"
      :data-callback="dataCallback"
      row-key="fileName"
      table-key="virtualWaveReplay"
      :max-height="'calc(100vh - 280px)'"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader="">
        <div class="flex flex-wrap gap-4 items-center header">
          <el-button type="primary" plain :icon="Refresh" @click="handleRefreshFiles">
            {{ t("device.virtualWaveReplay.getFiles") }}
          </el-button>
          <el-button type="primary" :icon="Upload" @click="handleAddFiles">
            {{ t("device.virtualWaveReplay.addFiles") }}
          </el-button>
        </div>
      </template>
      <!-- Expand -->
      <template #expand="scope">
        {{ scope.row }}
      </template>

      <!-- 表格操作 -->
      <template #operation="scope">
        <el-button type="primary" link :icon="VideoPlay" @click="playbackFaultRecord(scope.row)">
          {{ t("device.virtualWaveReplay.faultReplay") }}
        </el-button>
      </template>
    </ProTable>
  </div>

  <!-- 文件选择对话框 -->
  <CustomFileSelector ref="fileSelectorRef" :multiple="true" :file-types="['.cfg', '.dat', '.hdr', '.inf']" @confirm="handleFilesSelected" />

  <ProgressDialog ref="progressDialog"></ProgressDialog>
</template>

<script setup lang="ts" name="VirtualWaveReplay">
import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
import { useI18n } from "vue-i18n";
import { FileItem } from "@/api/interface/biz/debug/fileitem";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { Upload, VideoPlay, Refresh } from "@element-plus/icons-vue";
import { devicefileApi } from "@/api/modules/biz/debug/devicefile";
import { virtualDeviceApi } from "@/api/modules/biz/debug/virtualDevice";
import { MathUtils } from "@/utils/mathUtils";
import { ElMessageBox } from "element-plus";
import { createDebouncedSave, EventListenerManager } from "@/utils/performance";
import ProgressDialog from "../../dialog/ProgressDialog.vue";
import CustomFileSelector from "@/components/CustomFileSelector/index.vue";
import { useDebugStore } from "@/stores/modules/debug";
import { IECNotify } from "@/api/interface";
import { ipc } from "@/api/request/ipcRenderer";

const { addConsole } = useDebugStore();

// 透传装置ID
const props = defineProps<{ deviceId: string }>();
const progressDialog = ref();
const fileSelectorRef = ref();

// 文件下载进度跟踪
const downloadProgress = ref({
  isDownloading: false,
  currentFile: "",
  totalFiles: 0,
  completedFiles: 0,
  currentFileProgress: 0
});

// 事件监听器管理
const eventManager = new EventListenerManager();

// ProTable 实例
const proTable = ref<ProTableInstance>();
// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ filePath: "/shr/wave_replay" });

// 默认使用 /shr/wave_replay 目录
const selectType = ref("/shr/wave_replay");

const { t } = useI18n();

// 刷新获取文件列表
const handleRefreshFiles = () => {
  proTable.value?.getTableList();
};

// 添加录波回放文件
const handleAddFiles = () => {
  fileSelectorRef.value?.open();
};

// 处理文件选择完成
const handleFilesSelected = async (selectedData: any) => {
  // 处理单个文件或文件数组
  let selectedFiles: any[] = [];
  if (Array.isArray(selectedData)) {
    selectedFiles = selectedData;
  } else if (selectedData) {
    selectedFiles = [selectedData];
  }

  if (!selectedFiles || selectedFiles.length === 0) {
    return;
  }

  try {
    // 初始化下载进度
    downloadProgress.value = {
      isDownloading: true,
      currentFile: "",
      totalFiles: selectedFiles.length,
      completedFiles: 0,
      currentFileProgress: 0
    };

    progressDialog.value.show();
    progressDialog.value.setProgress(0, t("device.virtualWaveReplay.messages.preparingDownload"), false);

    // 构建文件下载参数 - 从FileItem对象中提取path属性
    const fileItems: any[] = [];

    for (let i = 0; i < selectedFiles.length; i++) {
      const fileItem = selectedFiles[i];
      // 提取文件路径，确保数据完全可序列化
      const filePath = fileItem.path || fileItem;
      fileItems.push({
        filePath: String(filePath),
        fileRow: i + 1
      });
    }

    // 创建纯净的参数对象
    const downloadParams = {
      remoteParentPath: String(selectType.value),
      fileItems: fileItems
    };

    console.log("下载参数:", JSON.stringify(downloadParams, null, 2));

    // 调用下载文件接口，将文件下载到装置的 /shr/wave_replay 目录
    const result = await devicefileApi.downloadDeviceFileByDevice(String(props.deviceId), downloadParams);

    if (Number(result.code) === 0) {
      // 如果没有进度更新，显示完成状态
      if (!downloadProgress.value.isDownloading || downloadProgress.value.completedFiles === downloadProgress.value.totalFiles) {
        progressDialog.value.setProgress(100, t("device.virtualWaveReplay.messages.downloadSuccess"), false);
        setTimeout(() => {
          ElMessageBox.alert(t("device.virtualWaveReplay.messages.downloadSuccess"), t("device.virtualParam.success"), {
            confirmButtonText: t("device.virtualParam.confirm"),
            type: "success"
          });
          addConsole(t("device.virtualWaveReplay.messages.downloadSuccess"));
          // 刷新表格数据
          proTable.value?.refresh();
        }, 500);
      }
    } else {
      ElMessageBox.alert(t("device.virtualWaveReplay.messages.downloadFailed") + ": " + result.msg, t("device.virtualParam.error"), {
        confirmButtonText: t("device.virtualParam.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    console.error("文件下载失败:", error);
    ElMessageBox.alert(t("device.virtualWaveReplay.messages.downloadFailed"), t("device.virtualParam.error"), {
      confirmButtonText: t("device.virtualParam.confirm"),
      type: "error"
    });
  } finally {
    // 重置下载状态
    downloadProgress.value.isDownloading = false;
    // 如果没有通过事件自动隐藏，延迟隐藏进度条
    setTimeout(() => {
      if (progressDialog.value.progressDialog.show) {
        progressDialog.value.hide();
      }
    }, 1000);
  }
};

// 故障录波回放功能
const playbackFaultRecord = async (row: FileItem) => {
  console.log("playbackFaultRecord", row);

  if (String(row.fileName).endsWith("\\")) {
    ElMessageBox.alert(t("device.virtualWaveReplay.errors.invalidFile"), t("common.error"), {
      type: "error",
      confirmButtonText: t("device.virtualParam.confirm")
    });
    return;
  }

  if (row.fileSize === 0) {
    ElMessageBox.alert(t("device.virtualWaveReplay.errors.fileSizeZero", { fileName: row.fileName }), t("common.error"), {
      type: "error",
      confirmButtonText: t("device.virtualParam.confirm")
    });
    return;
  }

  try {
    progressDialog.value.show();

    // 调用后台接口实现故障回放
    // 这里调用虚拟装置的故障回放接口
    const result = await virtualDeviceApi.playbackWaveReplayByDevice(props.deviceId, {
      fileName: row.fileName,
      filePath: selectType.value + "/" + row.fileName,
      fileSize: row.fileSize
    });

    if (Number(result.code) === 0) {
      ElMessageBox.alert(t("device.virtualWaveReplay.messages.playbackStarted", { fileName: row.fileName }), t("device.virtualParam.success"), {
        confirmButtonText: t("device.virtualParam.confirm"),
        type: "success"
      });
      addConsole(t("device.virtualWaveReplay.messages.playbackStarted", { fileName: row.fileName }));
    } else {
      ElMessageBox.alert(t("device.virtualWaveReplay.errors.playbackFailed") + ": " + result.msg, t("device.virtualParam.error"), {
        confirmButtonText: t("device.virtualParam.confirm"),
        type: "error"
      });
    }
  } catch (error) {
    console.error("故障录波回放失败:", error);
    ElMessageBox.alert(t("device.virtualWaveReplay.errors.playbackFailed"), t("common.error"), {
      type: "error",
      confirmButtonText: t("device.virtualParam.confirm")
    });
  } finally {
    progressDialog.value.hide();
  }
};

// 文件下载进度事件监听
const notifyMethod = (_event: unknown, notify: IECNotify): void => {
  // 多装置过滤：仅处理当前组件对应的 deviceId 事件
  if (notify.deviceId && notify.deviceId !== props.deviceId) return;

  if (notify.type === "fileDownload") {
    const notifyData = notify.data as any;
    const status = notifyData.status;
    const progress = notifyData.progress;
    const fileItem = notifyData.fileItem;
    const errorMsg = notifyData.errorMsg;

    if (!downloadProgress.value.isDownloading) return;

    let progressText = "";
    let percentage = 0;

    if (status === "CHECK_FILE_INFO") {
      progressText = t("device.virtualWaveReplay.messages.checkingFileInfo");
      percentage = 10;
    } else if (status === "INIT") {
      progressText = t("device.virtualWaveReplay.messages.initializingDownload");
      percentage = 15;
    } else if (status === "DATA_TRANSFER") {
      const fileName = fileItem?.filePath ? fileItem.filePath.split(/[\/\\]/).pop() : "";
      downloadProgress.value.currentFile = fileName;
      downloadProgress.value.currentFileProgress = progress?.filePercentage || 0;

      // 计算总体进度
      const fileProgress = (downloadProgress.value.completedFiles / downloadProgress.value.totalFiles) * 100;
      const currentFileContribution = (progress?.filePercentage || 0) / downloadProgress.value.totalFiles;
      percentage = Math.min(95, fileProgress + currentFileContribution);

      progressText = fileName
        ? t("device.virtualWaveReplay.messages.downloadingFile", {
            fileName,
            current: downloadProgress.value.completedFiles + 1,
            total: downloadProgress.value.totalFiles
          })
        : t("device.virtualWaveReplay.messages.downloading");
    } else if (status === "SINGLE_FILE_FINISH") {
      downloadProgress.value.completedFiles++;
      downloadProgress.value.currentFileProgress = 100;

      percentage = (downloadProgress.value.completedFiles / downloadProgress.value.totalFiles) * 100;
      progressText = t("device.virtualWaveReplay.messages.fileCompleted", {
        current: downloadProgress.value.completedFiles,
        total: downloadProgress.value.totalFiles
      });
    } else if (status === "ALL_FILE_FINISH") {
      downloadProgress.value.completedFiles = downloadProgress.value.totalFiles;
      downloadProgress.value.isDownloading = false;

      percentage = 100;
      progressText = t("device.virtualWaveReplay.messages.downloadSuccess");

      // 延迟隐藏进度条并刷新表格
      setTimeout(() => {
        progressDialog.value.hide();
        ElMessageBox.alert(t("device.virtualWaveReplay.messages.downloadSuccess"), t("device.virtualParam.success"), {
          confirmButtonText: t("device.virtualParam.confirm"),
          type: "success"
        });
        addConsole(t("device.virtualWaveReplay.messages.downloadSuccess"));
        proTable.value?.refresh();
      }, 500);
    } else if (status === "ERROR") {
      downloadProgress.value.isDownloading = false;
      progressText = t("device.virtualWaveReplay.messages.downloadError") + (errorMsg ? ": " + errorMsg : "");
      percentage = downloadProgress.value.completedFiles > 0 ? (downloadProgress.value.completedFiles / downloadProgress.value.totalFiles) * 100 : 0;

      setTimeout(() => {
        progressDialog.value.hide();
        ElMessageBox.alert(
          t("device.virtualWaveReplay.messages.downloadFailed") + (errorMsg ? ": " + errorMsg : ""),
          t("device.virtualParam.error"),
          {
            confirmButtonText: t("device.virtualParam.confirm"),
            type: "error"
          }
        );
      }, 500);
    }

    // 更新进度条
    progressDialog.value.setProgress(percentage, progressText, status === "DATA_TRANSFER");
  }
};

// dataCallback 是对于返回的表格数据做处理，如果你后台返回的数据不是 list && total 这些字段，可以在这里进行处理成这些字段
// 或者直接去 hooks/useTable.ts 文件中把字段改为你后端对应的就行
const dataCallback = (data: any) => {
  return {
    list: data.list,
    total: data.total
  };
};

// 获取文件列表 - 重新实现支持分页查询
const getWaveReplayList = async (params: any) => {
  let newParams: any = JSON.parse(JSON.stringify(params));
  newParams.createTime && (newParams.startTime = newParams.createTime[0]);
  newParams.createTime && (newParams.endTime = newParams.createTime[1]);
  newParams.filePath = selectType.value;
  delete newParams.createTime;

  // 添加分页参数
  newParams.pageNum = params.pageNum || 1;
  newParams.pageSize = params.pageSize || 10;

  try {
    progressDialog.value.show();

    // 使用新的分页查询接口
    const response = await devicefileApi.getDeviceFilePageByDevice(props.deviceId, newParams);

    if (Number(response.code) === 0) {
      // 处理分页数据结构
      const data = response.data || {};
      const items = Array.isArray(data.list) ? data.list : [];

      // 格式化文件大小
      items.forEach((item: any) => {
        item.fileSizeAs = MathUtils.formatBytes(item.fileSize);
      });

      return {
        list: items,
        total: data.total || 0
      };
    } else {
      ElMessageBox.alert(t("device.virtualWaveReplay.errors.getFilesFailed") + ": " + response.msg, t("device.virtualParam.error"), {
        confirmButtonText: t("device.virtualParam.confirm"),
        type: "error"
      });
      return {
        list: [],
        total: 0
      };
    }
  } catch (error) {
    console.error("获取文件列表失败:", error);
    ElMessageBox.alert(t("device.virtualWaveReplay.errors.getFilesFailed"), t("device.virtualParam.error"), {
      confirmButtonText: t("device.virtualParam.confirm"),
      type: "error"
    });
    return {
      list: [],
      total: 0
    };
  } finally {
    progressDialog.value.hide();
  }
};

// 表格配置项
const columns = reactive<ColumnProps<FileItem>[]>([
  { type: "index", label: t("device.virtualWaveReplay.serialNumber"), fixed: "left", width: 70 },
  {
    prop: "fileName",
    label: t("device.virtualWaveReplay.fileName"),
    width: 300,
    sortable: true,
    search: { el: "input", placeholder: t("device.virtualWaveReplay.searchFileName") }
  },
  {
    prop: "fileSize",
    label: t("device.virtualWaveReplay.fileSize"),
    width: 120,
    sortable: true,
    sortMethod: (a: any, b: any) => {
      // a、b 是整行数据，这里按原始字节数比较
      const sizeA = Number(a?.fileSize) || 0;
      const sizeB = Number(b?.fileSize) || 0;
      return sizeA - sizeB;
    },
    render: (scope: any) => {
      // 显示格式化后的文件大小
      return scope.row.fileSizeAs;
    }
  },
  {
    prop: "lastModified",
    label: t("device.virtualWaveReplay.lastModified"),
    sortable: true,
    sortMethod: (a: any, b: any) => {
      // 按时间排序
      const timeA = new Date(a.lastModified || 0).getTime() || 0;
      const timeB = new Date(b.lastModified || 0).getTime() || 0;
      return timeA - timeB;
    }
  },
  { prop: "operation", label: t("device.virtualWaveReplay.operation"), fixed: "right", width: 120 }
]);

// 删除了不需要的函数，现在使用ProTable的内置功能

// 使用性能优化的保存函数
const debouncedSaveData = createDebouncedSave(props.deviceId + ".waveReplayData", 1000);

// 文件下载进度事件监听
const notifyMethod = (_event: unknown, notify: IECNotify): void => {
  // 多装置过滤：仅处理当前组件对应的 deviceId 事件
  if (notify.deviceId && notify.deviceId !== props.deviceId) return;

  if (notify.type === "fileDownload") {
    const notifyData = notify.data as any;
    const status = notifyData.status;
    const progress = notifyData.progress;
    const fileItem = notifyData.fileItem;
    const errorMsg = notifyData.errorMsg;

    if (!downloadProgress.value.isDownloading) return;

    let progressText = "";
    let percentage = 0;

    if (status === "CHECK_FILE_INFO") {
      progressText = t("device.virtualWaveReplay.messages.checkingFileInfo");
      percentage = 10;
    } else if (status === "INIT") {
      progressText = t("device.virtualWaveReplay.messages.initializingDownload");
      percentage = 15;
    } else if (status === "DATA_TRANSFER") {
      const fileName = fileItem?.filePath ? fileItem.filePath.split(/[\/\\]/).pop() : "";
      downloadProgress.value.currentFile = fileName;
      downloadProgress.value.currentFileProgress = progress?.filePercentage || 0;

      // 计算总体进度
      const fileProgress = (downloadProgress.value.completedFiles / downloadProgress.value.totalFiles) * 100;
      const currentFileContribution = (progress?.filePercentage || 0) / downloadProgress.value.totalFiles;
      percentage = Math.min(95, fileProgress + currentFileContribution);

      progressText = fileName
        ? t("device.virtualWaveReplay.messages.downloadingFile", {
            fileName,
            current: downloadProgress.value.completedFiles + 1,
            total: downloadProgress.value.totalFiles
          })
        : t("device.virtualWaveReplay.messages.downloading");
    } else if (status === "SINGLE_FILE_FINISH") {
      downloadProgress.value.completedFiles++;
      downloadProgress.value.currentFileProgress = 100;

      percentage = (downloadProgress.value.completedFiles / downloadProgress.value.totalFiles) * 100;
      progressText = t("device.virtualWaveReplay.messages.fileCompleted", {
        current: downloadProgress.value.completedFiles,
        total: downloadProgress.value.totalFiles
      });
    } else if (status === "ALL_FILE_FINISH") {
      downloadProgress.value.completedFiles = downloadProgress.value.totalFiles;
      downloadProgress.value.isDownloading = false;

      percentage = 100;
      progressText = t("device.virtualWaveReplay.messages.downloadSuccess");

      // 延迟隐藏进度条并刷新表格
      setTimeout(() => {
        progressDialog.value.hide();
        ElMessageBox.alert(t("device.virtualWaveReplay.messages.downloadSuccess"), t("device.virtualParam.success"), {
          confirmButtonText: t("device.virtualParam.confirm"),
          type: "success"
        });
        addConsole(t("device.virtualWaveReplay.messages.downloadSuccess"));
        proTable.value?.refresh();
      }, 500);
    } else if (status === "ERROR") {
      downloadProgress.value.isDownloading = false;
      progressText = t("device.virtualWaveReplay.messages.downloadError") + (errorMsg ? ": " + errorMsg : "");
      percentage = downloadProgress.value.completedFiles > 0 ? (downloadProgress.value.completedFiles / downloadProgress.value.totalFiles) * 100 : 0;

      setTimeout(() => {
        progressDialog.value.hide();
        ElMessageBox.alert(
          t("device.virtualWaveReplay.messages.downloadFailed") + (errorMsg ? ": " + errorMsg : ""),
          t("device.virtualParam.error"),
          {
            confirmButtonText: t("device.virtualParam.confirm"),
            type: "error"
          }
        );
      }, 500);
    }

    // 更新进度条
    progressDialog.value.setProgress(percentage, progressText, status === "DATA_TRANSFER");
  }
};

onMounted(() => {
  addAllListeners();
  // 添加文件下载进度监听
  ipc.on("filedownload_notify", notifyMethod);
});

onBeforeUnmount(() => {
  // 立即保存一次数据
  debouncedSaveData.flush();
  removeAllListeners();
  // 移除文件下载进度监听
  ipc.removeAllListeners("filedownload_notify");
});

function removeAllListeners() {
  eventManager.cleanup();
}

function addAllListeners() {
  const saveHandler = () => debouncedSaveData.flush();
  window.addEventListener("beforeunload", saveHandler);
  eventManager.add("beforeunload", saveHandler);
}
</script>

<style lang="css" scoped>
.table-box {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
}
.header {
  margin-bottom: 8px;
}
</style>
