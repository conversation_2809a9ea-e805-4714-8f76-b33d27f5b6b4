/**
 * Virtual device French language pack
 */
export default {
  menus: {
    analog: {
      name: "Analogique",
      desc: "Paramètres analogiques"
    },
    digitalInput: {
      name: "<PERSON>tr<PERSON> Numérique",
      desc: "Paramètres d'entrée numérique"
    },
    digitalOutput: {
      name: "Sortie Numérique",
      desc: "Paramètres de sortie numérique"
    },
    fault: {
      name: "Défaut",
      desc: "Paramètres de défaut"
    },
    led: {
      name: "Paramètres LED",
      desc: "Paramètres LED"
    },
    waveReplay: {
      name: "Lecture de Défaut",
      desc: "Lecture de forme d'onde de défaut"
    }
  },
  items: {
    analog: {
      name: "Entrée Analogique 1",
      desc: "Entrée Analogique 1"
    },
    digitalInput: {
      name: "Entrée Numérique 1",
      desc: "Entrée Numérique 1"
    },
    digitalOutput: {
      name: "Sortie Numérique 1",
      desc: "Sortie Numérique 1"
    },
    fault: {
      name: "Signal de Défaut 1",
      desc: "Signal de Défaut 1"
    },
    led: {
      name: "Luminosité LED",
      desc: "Luminosité LED"
    },
    waveReplay: {
      name: "Fichier de Forme d'Onde",
      desc: "Fichier de Forme d'Onde"
    }
  },
  // Lié à l'importation/exportation
  importExport: {
    export: {
      success: "Exportation réussie",
      failed: "Échec de l'exportation",
      noData: "Aucune donnée à exporter",
      invalidPath: "Chemin de fichier invalide"
    },
    import: {
      success: "Importation réussie",
      failed: "Échec de l'importation",
      parsing: "Analyse du fichier Excel...",
      validating: "Validation des données...",
      updating: "Mise à jour des paramètres..."
    },
    template: {
      export: "Exporter le Modèle",
      name: "Modèle d'Importation"
    }
  },
  // En-têtes de colonnes Excel
  columns: {
    index: "Index",
    name: "Nom", 
    description: "Description",
    dataValue: "Valeur de Données",
    ang: "Angle"
  },
  // Messages d'erreur de validation
  validation: {
    required: {
      cmdType: "Le type de commande ne peut pas être vide",
      filePath: "Le chemin du fichier ne peut pas être vide",
      name: "Ligne {row}: Le champ nom ne peut pas être vide"
    },
    format: {
      fileExtension: "Seuls les fichiers Excel au format .xlsx sont pris en charge",
      dataValue: "Ligne {row}: La valeur de données doit être un nombre valide",
      ang: "Ligne {row}: L'angle doit être un nombre valide"
    },
    range: {
      angValue: "Ligne {row}: La valeur de l'angle doit être comprise entre -360 et 360 degrés"
    },
    file: {
      notExists: "Le fichier n'existe pas: {filePath}",
      noValidData: "Aucune donnée valide dans le fichier Excel",
      noValidRows: "Aucune donnée valide à importer",
      parseError: "Échec de l'analyse du fichier Excel: {error}",
      exportError: "Échec de l'exportation du fichier Excel: {error}"
    },
    data: {
      validationFailed: "Échec de la validation des données:\n{errors}",
      updateFailed: "Les données d'importation ont été analysées avec succès, mais la mise à jour a échoué"
    }
  },
  // Messages d'opération
  messages: {
    exportStart: "Début de l'exportation des paramètres du dispositif virtuel",
    exportSuccess: "Paramètres du dispositif virtuel exportés avec succès: {filePath}, nombre de données: {count}",
    importStart: "Début de l'importation des paramètres du dispositif virtuel",
    importSuccess: "Paramètres du dispositif virtuel importés et mis à jour avec succès: {filePath}, nombre mis à jour: {count}",
    parseSuccess: "Fichier Excel analysé avec succès, nombre de données: {count}",
    validDataCount: "Nombre de données valides: {count}",
    templateExportSuccess: "Modèle d'importation exporté avec succès: {filePath}"
  }
};