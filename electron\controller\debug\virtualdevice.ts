"use strict";

import { deviceConnectService } from "../../service/debug/deviceconnect";
import { virtualDeviceService } from "../../service/debug/virtualdevice";
import { logger } from "ee-core/log";
import { t } from "../../data/i18n/i18n";
import {
  ERROR_CODES,
  ERROR_MESSAGES,
  handleInternalError,
  handleConnectionError,
  handleCustomResponse,
  handleErrorResponse,
} from "../../data/debug/errorCodes";
import { ApiResponse } from "../../data/debug/apiResponse";
import { IECReq } from "../../interface/debug/request";

/**
 * 虚拟化装置Controller
 * <AUTHOR>
 * @class
 */
class VirtualDeviceController {
  constructor() {
    logger.info(`[VirtualDeviceController] 虚拟化装置控制器初始化完成`);
  }

  /**
   * 获取虚拟化装置参数
   * @param req 请求体，包含cmdType等参数
   * @returns ApiResponse 虚拟化装置参数列表
   */
  async getVirtualParams(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[VirtualDeviceController] getVirtualParams - 开始获取虚拟化装置参数:`,
      req
    );

    if (!(await deviceConnectService.checkConnection(req))) {
      logger.warn(
        `[VirtualDeviceController] getVirtualParams - 设备未连接，无法获取虚拟化装置参数`
      );
      return handleConnectionError();
    }

    try {
      logger.debug(
        `[VirtualDeviceController] getVirtualParams - 设备已连接，调用服务层获取虚拟化装置参数`
      );
      const result = await virtualDeviceService.getVirtualParams(req);

      logger.info(
        `[VirtualDeviceController] getVirtualParams - 成功获取虚拟化装置参数，结果数量: ${result?.list?.length || 0}`
      );
      return new ApiResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        `[VirtualDeviceController] getVirtualParams - 获取虚拟化装置参数异常:`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 更新虚拟化装置参数
   * @param req 请求体，包含cmdType和params等参数
   * @returns ApiResponse 更新结果
   */
  async updateVirtualParams(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[VirtualDeviceController] updateVirtualParams - 开始更新虚拟化装置参数:`,
      req
    );

    if (!(await deviceConnectService.checkConnection(req))) {
      logger.warn(
        `[VirtualDeviceController] updateVirtualParams - 设备未连接，无法更新虚拟化装置参数`
      );
      return handleConnectionError();
    }

    try {
      logger.debug(
        `[VirtualDeviceController] updateVirtualParams - 设备已连接，调用服务层更新虚拟化装置参数`
      );
      const result = await virtualDeviceService.updateVirtualParams(req);

      logger.info(
        `[VirtualDeviceController] updateVirtualParams - 成功更新虚拟化装置参数，结果:`,
        result
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        `[VirtualDeviceController] updateVirtualParams - 更新虚拟化装置参数异常:`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 导出虚拟化装置参数
   * @param req 请求体，包含cmdType、filePath等参数
   * @returns ApiResponse 导出结果
   */
  async exportVirtualParams(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[VirtualDeviceController] exportVirtualParams - 开始导出虚拟化装置参数:`,
      req
    );

    if (!(await deviceConnectService.checkConnection(req))) {
      logger.warn(
        `[VirtualDeviceController] exportVirtualParams - 设备未连接，无法导出虚拟化装置参数`
      );
      return handleConnectionError();
    }

    try {
      logger.debug(
        `[VirtualDeviceController] exportVirtualParams - 设备已连接，调用服务层导出虚拟化装置参数`
      );
      const result = await virtualDeviceService.exportVirtualParams(req);

      logger.info(
        `[VirtualDeviceController] exportVirtualParams - 成功导出虚拟化装置参数，结果:`,
        result
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        `[VirtualDeviceController] exportVirtualParams - 导出虚拟化装置参数异常:`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 导入虚拟化装置参数
   * @param req 请求体，包含cmdType、filePath等参数
   * @returns ApiResponse 导入结果
   */
  async importVirtualParams(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[VirtualDeviceController] importVirtualParams - 开始导入虚拟化装置参数:`,
      req
    );

    if (!(await deviceConnectService.checkConnection(req))) {
      logger.warn(
        `[VirtualDeviceController] importVirtualParams - 设备未连接，无法导入虚拟化装置参数`
      );
      return handleConnectionError();
    }

    try {
      logger.debug(
        `[VirtualDeviceController] importVirtualParams - 设备已连接，调用服务层导入虚拟化装置参数`
      );
      const result = await virtualDeviceService.importVirtualParams(req);

      logger.info(
        `[VirtualDeviceController] importVirtualParams - 成功导入虚拟化装置参数，结果:`,
        result
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        `[VirtualDeviceController] importVirtualParams - 导入虚拟化装置参数异常:`,
        error
      );
      return handleErrorResponse(error);
    }
  }

  /**
   * 故障录波回放
   * @param req 请求体，包含fileName、filePath、fileSize等参数
   * @returns ApiResponse 回放结果
   */
  async playbackWaveReplay(req: IECReq<any>): Promise<ApiResponse> {
    logger.info(
      `[VirtualDeviceController] playbackWaveReplay - 开始故障录波回放:`,
      req
    );

    if (!(await deviceConnectService.checkConnection(req))) {
      logger.warn(
        `[VirtualDeviceController] playbackWaveReplay - 设备未连接，无法进行故障录波回放`
      );
      return handleConnectionError();
    }

    try {
      logger.debug(
        `[VirtualDeviceController] playbackWaveReplay - 设备已连接，调用服务层进行故障录波回放`
      );
      const result = await virtualDeviceService.playbackWaveReplay(req);

      logger.info(
        `[VirtualDeviceController] playbackWaveReplay - 成功进行故障录波回放，结果:`,
        result
      );
      return handleCustomResponse(
        ERROR_CODES.SUCCESS,
        ERROR_MESSAGES.SUCCESS,
        result
      );
    } catch (error) {
      logger.error(
        `[VirtualDeviceController] playbackWaveReplay - 故障录波回放异常:`,
        error
      );
      return handleErrorResponse(error);
    }
  }
}

VirtualDeviceController.toString = () => "[class VirtualDeviceController]";
export default VirtualDeviceController;